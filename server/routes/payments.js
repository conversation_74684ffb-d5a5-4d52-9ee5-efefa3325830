const express = require('express');
const router = express.Router();

const OrderPrisma = require('../models/OrderPrisma');
const UserPrisma = require('../models/UserPrisma');
const ResponseHelper = require('../utils/response');

/**
 * 接口 10: 微信支付回调
 * POST /api/v1/payments/wechat/notify
 * 
 * 逻辑: 这是给微信服务器调用的接口。接收并验证微信的异步通知。（模拟成功）
 * 数据库: 若支付成功，根据订单号更新 orders 表状态为 paid。然后，根据订单中的 user_id 和 package_id，为用户的 publishing_credits 增加相应的条数。
 */
router.post('/wechat/notify', async (req, res) => {
  try {
    // 模拟微信支付回调数据
    const { 
      out_trade_no, // 订单号
      transaction_id, // 微信支付交易号
      trade_state = 'SUCCESS' // 支付状态
    } = req.body;

    console.log('收到微信支付回调:', req.body);

    // 验证必要参数
    if (!out_trade_no) {
      return res.status(400).send('FAIL');
    }

    // 查找订单
    const order = await OrderPrisma.findByOrderNumber(out_trade_no, {
      include: { user: true, package: true }
    });

    if (!order) {
      console.error('订单不存在:', out_trade_no);
      return res.status(400).send('FAIL');
    }

    // 检查订单状态
    if (order.status === 'paid') {
      console.log('订单已处理:', out_trade_no);
      return res.send('SUCCESS');
    }

    // 模拟支付成功处理
    if (trade_state === 'SUCCESS') {
      // 更新订单状态为已支付
      const updatedOrder = await OrderPrisma.markAsPaid(out_trade_no, {
        transaction_id: transaction_id || `mock_txn_${Date.now()}`,
        payment_method: 'wechat'
      });

      if (updatedOrder && updatedOrder.package) {
        // 为用户增加发布条数
        await UserPrisma.addCredits(
          updatedOrder.user_id, 
          updatedOrder.package.credits_amount
        );

        console.log(`支付成功处理完成: 订单${out_trade_no}, 用户${updatedOrder.user_id}增加${updatedOrder.package.credits_amount}条发布次数`);
      }

      return res.send('SUCCESS');
    } else {
      // 支付失败，更新订单状态
      await OrderPrisma.update(order.id, {
        status: 'failed',
        transaction_id: transaction_id || null
      });

      console.log('支付失败:', out_trade_no, trade_state);
      return res.send('SUCCESS');
    }

  } catch (error) {
    console.error('微信支付回调处理错误:', error);
    return res.status(500).send('FAIL');
  }
});

/**
 * 模拟支付成功接口（开发测试用）
 * POST /api/v1/payments/mock/success
 */
router.post('/mock/success', async (req, res) => {
  try {
    const { order_number } = req.body;

    if (!order_number) {
      return ResponseHelper.validationError(res, ['order_number 是必填参数']);
    }

    // 查找订单
    const order = await OrderPrisma.findByOrderNumber(order_number, {
      include: { user: true, package: true }
    });

    if (!order) {
      return ResponseHelper.notFound(res, '订单不存在');
    }

    if (order.status === 'paid') {
      return ResponseHelper.error(res, '订单已支付', 400);
    }

    // 模拟支付成功
    const updatedOrder = await OrderPrisma.markAsPaid(order_number, {
      transaction_id: `mock_txn_${Date.now()}`,
      payment_method: 'wechat'
    });

    if (updatedOrder && updatedOrder.package) {
      // 为用户增加发布条数
      await UserPrisma.addCredits(
        updatedOrder.user_id, 
        updatedOrder.package.credits_amount
      );
    }

    ResponseHelper.success(res, {
      order: updatedOrder.toJSON()
    }, '模拟支付成功');

  } catch (error) {
    console.error('模拟支付错误:', error);
    ResponseHelper.serverError(res, '模拟支付失败', error);
  }
});

module.exports = router;
