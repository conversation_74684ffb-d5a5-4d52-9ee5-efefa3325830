const { prisma } = require('../config/prisma');

class UserPrisma {
  constructor(data) {
    this.id = data.id;
    this.openid = data.openid;
    this.nickname = data.nickname;
    this.avatar_url = data.avatar_url;
    this.phone_number = data.phone_number;
    this.status = data.status;
    this.publishing_credits = data.publishing_credits;
    this.inviter_id = data.inviter_id;
    this.created_at = data.created_at;
    this.updated_at = data.updated_at;
  }

  /**
   * 根据openid查找用户
   * @param {string} openid 微信openid
   * @returns {Promise<UserPrisma|null>}
   */
  static async findByOpenid(openid) {
    const user = await prisma.users.findUnique({
      where: { openid }
    });
    return user ? new UserPrisma(user) : null;
  }

  /**
   * 根据ID查找用户
   * @param {number} id 用户ID
   * @returns {Promise<UserPrisma|null>}
   */
  static async findById(id) {
    const user = await prisma.users.findUnique({
      where: { id: BigInt(id) }
    });
    return user ? new UserPrisma(user) : null;
  }

  /**
   * 创建新用户
   * @param {Object} userData 用户数据
   * @returns {Promise<UserPrisma>}
   */
  static async create(userData) {
    const user = await prisma.users.create({
      data: {
        openid: userData.openid,
        nickname: userData.nickname || null,
        avatar_url: userData.avatar_url || null,
        phone_number: userData.phone_number || null,
        status: userData.status || 'inactive',
        publishing_credits: userData.publishing_credits || 0,
        inviter_id: userData.inviter_id ? BigInt(userData.inviter_id) : null
      }
    });
    return new UserPrisma(user);
  }

  /**
   * 更新用户信息
   * @param {number} id 用户ID
   * @param {Object} updateData 更新数据
   * @returns {Promise<UserPrisma>}
   */
  static async update(id, updateData) {
    // 处理BigInt字段
    const data = { ...updateData };
    if (data.inviter_id !== undefined && data.inviter_id !== null) {
      data.inviter_id = BigInt(data.inviter_id);
    }

    const user = await prisma.users.update({
      where: { id: BigInt(id) },
      data
    });
    return new UserPrisma(user);
  }

  /**
   * 更新最后登录时间
   * @param {number} id 用户ID
   * @returns {Promise<void>}
   */
  static async updateLastLogin(id) {
    await prisma.users.update({
      where: { id: BigInt(id) },
      data: { updated_at: new Date() }
    });
  }

  /**
   * 增加发布额度
   * @param {number} id 用户ID
   * @param {number} credits 增加的额度
   * @returns {Promise<UserPrisma>}
   */
  static async addCredits(id, credits) {
    const user = await prisma.users.update({
      where: { id: BigInt(id) },
      data: {
        publishing_credits: {
          increment: credits
        }
      }
    });
    return new UserPrisma(user);
  }

  /**
   * 减少发布额度
   * @param {number} id 用户ID
   * @param {number} credits 减少的额度
   * @returns {Promise<UserPrisma>}
   */
  static async reduceCredits(id, credits = 1) {
    // 先检查用户当前额度
    const currentUser = await prisma.users.findUnique({
      where: { id: BigInt(id) },
      select: { publishing_credits: true }
    });

    if (!currentUser || currentUser.publishing_credits < credits) {
      throw new Error('发布额度不足');
    }

    const user = await prisma.users.update({
      where: { id: BigInt(id) },
      data: {
        publishing_credits: {
          decrement: credits
        }
      }
    });
    return new UserPrisma(user);
  }

  /**
   * 更新发布额度
   * @param {number} id 用户ID
   * @param {number} credits 新的额度值
   * @returns {Promise<UserPrisma>}
   */
  static async updateCredits(id, credits) {
    const user = await prisma.users.update({
      where: { id: BigInt(id) },
      data: {
        publishing_credits: credits
      }
    });
    return new UserPrisma(user);
  }

  /**
   * 激活用户
   * @param {number} id 用户ID
   * @returns {Promise<UserPrisma>}
   */
  static async activate(id) {
    return await UserPrisma.update(id, { status: 'active' });
  }

  /**
   * 获取用户统计信息
   * @returns {Promise<Object>}
   */
  static async getStats() {
    const [total, active, today] = await Promise.all([
      prisma.users.count(),
      prisma.users.count({
        where: { status: 'active' }
      }),
      prisma.users.count({
        where: {
          created_at: {
            gte: new Date(new Date().setHours(0, 0, 0, 0))
          }
        }
      })
    ]);

    return { total, active, today };
  }

  /**
   * 获取用户列表（分页）
   * @param {Object} options 查询选项
   * @returns {Promise<Object>}
   */
  static async getList(options = {}) {
    const {
      page = 1,
      pageSize = 10,
      status,
      search
    } = options;

    const pageNum = parseInt(page);
    const pageSizeNum = parseInt(pageSize);
    const skip = (pageNum - 1) * pageSizeNum;

    // 构建where条件
    const where = {};
    
    if (status) {
      where.status = status;
    }

    if (search) {
      where.OR = [
        { nickname: { contains: search } },
        { phone_number: { contains: search } }
      ];
    }

    // 并行查询总数和列表数据
    const [total, users] = await Promise.all([
      prisma.users.count({ where }),
      prisma.users.findMany({
        where,
        orderBy: { created_at: 'desc' },
        skip,
        take: pageSizeNum
      })
    ]);

    return {
      data: users.map(user => new UserPrisma(user)),
      pagination: {
        page: pageNum,
        pageSize: pageSizeNum,
        total,
        totalPages: Math.ceil(total / pageSizeNum)
      }
    };
  }

  /**
   * 转换为JSON对象（隐藏敏感信息）
   * @returns {Object}
   */
  toJSON() {
    return {
      id: this.id.toString(), // BigInt转字符串
      nickname: this.nickname,
      avatar_url: this.avatar_url,
      phone_number: this.phone_number,
      status: this.status,
      publishing_credits: this.publishing_credits,
      created_at: this.created_at,
      updated_at: this.updated_at
    };
  }
}

module.exports = UserPrisma;
